package WayofTime.alchemicalWizardry.common.omega;

public class OmegaStructureParameters 
{
	public final int stability;
	public final int enchantability;
	public final int enchantmentLevel;
	
	public OmegaStructureParameters(int stability, int enchantability, int enchantmentLevel)
	{
		this.stability = stability;
		this.enchantability = enchantability;
		this.enchantmentLevel = enchantmentLevel;
	}
}
