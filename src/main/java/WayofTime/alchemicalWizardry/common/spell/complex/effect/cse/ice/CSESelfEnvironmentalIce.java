package WayofTime.alchemicalWizardry.common.spell.complex.effect.cse.ice;

import WayofTime.alchemicalWizardry.api.spell.ComplexSpellEffect;
import WayofTime.alchemicalWizardry.api.spell.ComplexSpellModifier;
import WayofTime.alchemicalWizardry.api.spell.ComplexSpellType;
import WayofTime.alchemicalWizardry.api.spell.SpellParadigm;
import WayofTime.alchemicalWizardry.api.spell.SpellParadigmSelf;
import WayofTime.alchemicalWizardry.common.spell.complex.effect.impactEffects.ice.SelfEnvironmentalIce;

public class CSESelfEnvironmentalIce extends ComplexSpellEffect
{
	public CSESelfEnvironmentalIce() 
	{
		super(ComplexSpellType.ICE, ComplexSpellModifier.ENVIRONMENTAL);
	}
	
	public CSESelfEnvironmentalIce(int power, int cost, int potency)
	{
		this();
		
		this.powerEnhancement = power;
		this.costEnhancement = cost;
		this.potencyEnhancement = potency;
	}

	@Override
	public void modifyParadigm(SpellParadigm parad) 
	{
		if(parad instanceof SpellParadigmSelf)
		{
			((SpellParadigmSelf)parad).addSelfSpellEffect(new SelfEnvironmentalIce(this.powerEnhancement, this.potencyEnhancement, this.costEnhancement));
		}
	}

	@Override
	public ComplexSpellEffect copy(int power, int cost, int potency) 
	{
		return new CSESelfEnvironmentalIce(power, cost, potency);
	}

	@Override
	public int getCostOfEffect() 
	{
        return (int) (10 * (1.5 * potencyEnhancement + 1) * (3 * powerEnhancement + 1) * Math.pow(0.85, costEnhancement));
	}
}
