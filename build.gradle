buildscript {
    repositories {
        mavenCentral()
        maven { url = "http://files.minecraftforge.net/maven" }
        maven { url = "https://oss.sonatype.org/content/repositories/snapshots/" }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:1.2-SNAPSHOT'
		classpath 'org.ajoberstar:gradle-git:0.10.1'
    }
}

apply plugin: 'forge'

ext.configFile = file "build.properties"
configFile.withReader {
    def prop = new Properties()
    prop.load(it)
    project.ext.config = new ConfigSlurper().parse prop
}

version = config.mc_version + "-" + config.mod_version + "-" + config.build_number
group= config.package_group
archivesBaseName = config.mod_name

import org.ajoberstar.grgit.Grgit

def gitHash = 'unknown'
if (new File(projectDir, '.git').exists()) {
    def repo = Grgit.open(project.file('.'))
    gitHash = repo.log().find().abbreviatedId
}

repositories {
    maven {
        name 'CB Maven FS'
        url "http://chickenbones.net/maven/"
    }
    maven {
        name 'DVS1 Maven FS'
        url 'http://dvs1.progwml6.com/files/maven'
    }
    maven {
    	name 'TehNut Repo'
    	url 'http://tehnut.info/maven/'
    }
    maven {
        name 'tterrag Repo'
        url 'http://maven.tterrag.com/'
    }
	ivy {
		name "MineTweaker3"
        artifactPattern "http://minetweaker3.powerofbytes.com/download/[module]-[revision].[ext]"
    }	
}

dependencies {
    compile "codechicken:CodeChickenLib:" + config.mc_version + "-" + config.cclib_version + ":dev"
    compile "codechicken:CodeChickenCore:" + config.mc_version + "-" + config.ccc_version + ":dev"
    compile "codechicken:NotEnoughItems:" + config.mc_version + "-" + config.nei_version + ":dev"
	compile name: 'MineTweaker3', version: config.minetweaker_version, ext: 'jar'
	compile "codechicken:ForgeMultipart:1.7.10-1.1.0.314:dev"
	compile "info.amerifrance.guideapi:Guide-API:" + config.mc_version + "-" + config.guideapi_version + ":deobf"
    compile "team.chisel:Chisel:" + config.chisel_version + ":api"
}

minecraft {
    version = config.mc_version + "-" + config.forge_version
    runDir = "run"
	
	replaceIn "GAPIChecker.java"
	replace "@VERSION@", config.guideapi_version
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include '**/*.info'
        include '**/*.properties'

        expand 'version': project.version, 'mcversion': project.minecraft.version
    }
    from(sourceSets.main.resources.srcDirs) {
        exclude '**/*.info'
        exclude '**/*.properties'
		exclude '**/*.db'
    }
}

jar {
    dependsOn "incrementBuildNumber"
	classifier = ''
	 manifest.mainAttributes(
            "Built-By": System.getProperty('user.name'),
            "Created-By": "${System.getProperty('java.vm.version')} + (${System.getProperty('java.vm.vendor')})",
            "Implementation-Title": project.name,
            "Implementation-Version": project.version,
            "Git-Hash": gitHash
    )
}

// add a source jar
task sourceJar(type: Jar) {
    from sourceSets.main.allSource
    classifier = 'sources'
}

// add a javadoc jar
task javadocJar(type: Jar, dependsOn: javadoc) {
    from javadoc.destinationDir
    classifier = 'javadoc'
}

// because the normal output has been made to be obfuscated
task deobfJar(type: Jar) {
    from sourceSets.main.output
    classifier = 'deobf'
}

tasks.build.dependsOn sourceJar, javadocJar, deobfJar

tasks.withType(JavaCompile) { task ->
    task.options.encoding = 'UTF-8'
}

task("incrementBuildNumber") {
	// increment build number
	doFirst {
		// increment
		config.build_number = (config.build_number.toString().toInteger()) + 1

		// write back to the file
		configFile.withWriter {
			config.toProperties().store(it, "")
		}
	}
}

// Uncomment this line if you want to auto-upload to CurseForge when you build. This requires you to setup curse.gradle yourself.
// fileTree('gradle').include('curse.gradle').collect().sort().each { apply from: it }